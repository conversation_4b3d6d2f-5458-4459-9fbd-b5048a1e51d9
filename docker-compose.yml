version: '3.8'

services:
  # NestJS Application
  nestjs-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE}
      - MCP_SERVERS=${MCP_SERVERS}
      - CONTEXT_DESCRIPTION_FILE=${CONTEXT_DESCRIPTION_FILE}
      - CONTEXT_SYSTEM_PROMPT_FILE=${CONTEXT_SYSTEM_PROMPT_FILE}
    depends_on:
      - mcp-postgres
    networks:
      - app-network
    volumes:
      - ./docs:/app/docs:ro

  # PostgreSQL MCP Server
  mcp-postgres:
    build: ./mcp-server
    environment:
      - DATABASE_URL=${DATABASE_URL}
    networks:
      - app-network
    # MCP server is only accessible within the docker network
    # No external ports exposed for security

networks:
  app-network:
    driver: bridge
