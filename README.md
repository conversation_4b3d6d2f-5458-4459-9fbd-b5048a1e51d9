# SW Search Agent

A sophisticated search agent that enables natural language querying of PostgreSQL databases using AI. Built with TypeScript, NestJS, and integrating OpenAI/LM Studio with Model Context Protocol (MCP) for secure database access.

## 🏗️ Architecture Overview

The application implements a **rolling messages array pattern** for AI workflows, where:
- System prompts initialize the conversation context
- User queries are added to the message history
- AI assistant responses and tool calls are tracked
- Database tool results are injected back as tool messages
- The conversation maintains context across multiple interactions

### Core Workflow
```
User Query → AI Assistant → Tool Calls → MCP Database → Tool Results → Final Response
     ↓                                                                        ↑
Message Array: [system, user, assistant, tool, assistant, tool, ...]
```

## 🛠️ Technology Stack

### Backend
- **Framework**: NestJS (Node.js)
- **Language**: TypeScript
- **AI Integration**: OpenAI SDK (compatible with LM Studio)
- **Database**: PostgreSQL (read-only access)
- **Protocol**: Real Model Context Protocol (MCP) with @modelcontextprotocol/server-postgres
- **MCP Architecture**: Containerized MCP server with remote communication
- **Context System**: File-based system prompts and database metadata
- **Configuration**: Environment-based with @nestjs/config
- **Containerization**: <PERSON><PERSON> and <PERSON>er Compose for deployment

### Frontend
- **UI**: Vanilla HTML/CSS/JavaScript
- **Styling**: Modern CSS with responsive design
- **Communication**: REST API with fetch()

### Development Tools
- **Build**: TypeScript compiler with NestJS CLI
- **Code Quality**: ESLint + Prettier
- **Process Management**: npm scripts with hot reload

## 📁 Project Structure

```
sw-search-agent/
├── src/
│   ├── main.ts                 # Application entry point
│   ├── app.module.ts           # Root module with all imports
│   ├── config/
│   │   └── configuration.ts    # Environment configuration loader
│   ├── auth/                   # Authentication module (placeholder)
│   │   └── auth.module.ts      # Future auth implementation
│   ├── openai/                 # OpenAI/LM Studio integration
│   │   ├── openai.module.ts    # OpenAI module definition
│   │   └── openai.service.ts   # Simplified OpenAI client service
│   ├── mcp/                    # Remote Model Context Protocol client
│   │   ├── mcp.module.ts       # MCP module definition
│   │   └── mcp.service.ts      # Remote MCP client implementation
│   ├── context/                # File-based context system
│   │   ├── context.module.ts   # Context module definition
│   │   └── context.service.ts  # System prompt and metadata service
│   └── search/                 # Main search functionality
│       ├── search.module.ts    # Search module definition
│       ├── search.controller.ts # REST API endpoints
│       └── search.service.ts   # Core search logic
├── mcp-server/                 # Standalone MCP server container
│   ├── Dockerfile             # MCP server Docker configuration
│   └── start-mcp-server.sh    # MCP server startup script
├── public/                     # Static web assets
│   ├── index.html             # Main UI page
│   ├── style.css              # Application styling
│   └── script.js              # Frontend JavaScript
├── docs/                      # Documentation and configuration
│   ├── database-description.md # Database metadata for AI context
│   └── system-prompt.txt      # AI system prompt template
├── scripts/                   # Development and deployment scripts
│   └── dev-setup.sh           # Development environment setup
├── Dockerfile                 # NestJS app Docker configuration
├── docker-compose.yml         # Multi-container deployment
├── .env                       # Environment variables
├── .env.example              # Environment template
├── package.json              # Dependencies and scripts
├── tsconfig.json             # TypeScript configuration
├── .eslintrc.js              # ESLint configuration
├── .prettierrc               # Prettier configuration
└── nest-cli.json             # NestJS CLI configuration
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Application
PORT=3000
NODE_ENV=development

# OpenAI/LM Studio Configuration
OPENAI_API_KEY=lm-studio
OPENAI_BASE_URL=http://127.0.0.1:1234/v1
OPENAI_MODEL=devstral-small-2505-mlx
OPENAI_TEMPERATURE=0.1

# MCP Configuration
MCP_SERVERS=http://mcp-postgres:3001

# Context Configuration
CONTEXT_DESCRIPTION_FILE=docs/database-description.md
CONTEXT_SYSTEM_PROMPT_FILE=docs/system-prompt.txt

# PostgreSQL Database (for MCP server container only)
DATABASE_URL=postgresql://postgres:<EMAIL>:5433/sw_dev
```

## 🗄️ Database Integration

### Containerized MCP Architecture
The application uses a containerized Model Context Protocol implementation:

- **MCP Server**: Standalone MCP server using official `@modelcontextprotocol/sdk` with HTTP transport
- **MCP Client**: NestJS app connects as MCP client using `StreamableHTTPClientTransport`
- **Container Isolation**: MCP server runs in separate Docker container
- **Network Security**: MCP server accessible only within Docker network
- **Zero Database Dependencies**: NestJS app has no direct database connectivity
- **Simplified Deployment**: Docker Compose orchestrates both services

### File-Based Context System
- **External System Prompts**: AI prompts loaded from `docs/system-prompt.txt`
- **Database Metadata**: Additional context loaded from `docs/database-description.md`
- **Runtime Schema Discovery**: Actual database schema discovered by MCP server at runtime
- **Version Controlled**: All context files are part of the codebase and version controlled
- **Configurable Paths**: Customize file locations via environment variables
- **Always Enabled**: Static description inclusion is always active (no toggle needed)

### MCP Protocol Communication
The containerized MCP server implements the official Model Context Protocol:

#### Endpoints
- **`GET /health`**: Health check and service status
- **`POST /mcp`**: MCP protocol endpoint for client-server communication

#### Available Tools
1. **`read_query`**: Execute read-only SQL queries with enhanced safety
2. **`list_tables`**: List all tables with metadata
3. **`describe_table`**: Get detailed table schema information

#### MCP Client Integration
The NestJS application connects as an MCP client using:
- `StreamableHTTPClientTransport` for HTTP-based communication
- Official `@modelcontextprotocol/sdk` client implementation
- Automatic tool discovery and OpenAI-compatible tool calling

### Security Features
- **Read-only access**: Only SELECT statements are permitted by MCP server
- **Container isolation**: MCP server runs in separate Docker container
- **Network isolation**: MCP server accessible only within Docker network
- **Zero database exposure**: NestJS app has no direct database credentials
- **Error handling**: Safe error propagation without sensitive data exposure

## 🌐 API Endpoints

### REST API
- `POST /api/search` - Main search endpoint
  - Body: `{ query: string, conversationId?: string }`
  - Returns: `{ response: string, conversationId: string, toolCalls: array }`

- `GET /api/search/conversation/:id` - Retrieve conversation history
- `DELETE /api/search/conversation/:id` - Clear conversation

### Static Assets
- `GET /` - Web UI (served from public/)
- `GET /style.css` - Application styles
- `GET /script.js` - Frontend JavaScript

## 🐳 Docker Deployment

The application is fully containerized with Docker Compose for easy deployment:

### Architecture
- **nestjs-app**: Main NestJS application container
- **mcp-postgres**: Standalone MCP PostgreSQL server container
- **app-network**: Internal Docker network for service communication

### Quick Start
```bash
# Clone and setup
git clone <repository>
cd sw-search-agent
./scripts/dev-setup.sh

# Start with Docker Compose
docker-compose up --build

# Access the application
open http://localhost:3000
```

### Container Configuration
- **NestJS App**: Exposed on port 3000
- **MCP Server**: Internal only (no external access)
- **Shared Network**: Secure communication between containers
- **Environment Variables**: Configurable via .env file

## 🚀 Available Scripts

```bash
# Development
npm run dev      # Start development server with hot reload
npm run start    # Start production server
npm run build    # Build the application
npm run lint     # Run ESLint
npm run format   # Format code with Prettier

# Docker Deployment
docker-compose up --build    # Start all services with Docker
docker-compose down          # Stop all services

# Setup
./scripts/dev-setup.sh       # Initialize development environment
```

## 📊 Logging

The application implements comprehensive logging across all modules:

- **Request/Response tracking** with timing metrics
- **Database operation logging** with query performance
- **AI interaction logging** with token usage
- **Tool execution tracking** with success/failure status
- **Error logging** with full stack traces

Log levels: `LOG`, `ERROR`, `WARN` with contextual information.

## 🔄 Message Flow Architecture

### Conversation Management
- **Conversation ID**: Unique identifier for each user session
- **Message Persistence**: In-memory storage of conversation history
- **Context Preservation**: System prompts and conversation state maintained

### Tool Integration Pattern
```typescript
// Rolling message array pattern
messages = [
  { role: 'system', content: 'System prompt...' },
  { role: 'user', content: 'User query' },
  { role: 'assistant', content: 'Response', tool_calls: [...] },
  { role: 'tool', content: 'Tool result', tool_call_id: 'id' },
  { role: 'assistant', content: 'Final response' }
]
```

## 🛡️ Security Considerations

- **Database Access**: Read-only PostgreSQL access with query validation
- **Environment Variables**: Sensitive configuration externalized
- **CORS**: Enabled for frontend communication
- **Error Handling**: Safe error messages without sensitive data exposure
- **Input Validation**: Query sanitization and type checking

## 🎯 Use Cases

- **Database Exploration**: "Show me all tables in the database"
- **Schema Analysis**: "What's the structure of the users table?"
- **Data Querying**: "Find all products with price greater than 100"
- **Analytics**: "How many records are in each table?"
- **Relationship Discovery**: "Show me the foreign key relationships"

## 🔮 Future Enhancements

- **Authentication**: User management and access control
- **Query Caching**: Redis integration for performance
- **Advanced MCP Tools**: Write operations, stored procedures
- **Multi-database Support**: Multiple PostgreSQL instances
- **Export Functionality**: CSV, JSON data export
- **Query History**: Persistent conversation storage
- **Remote MCP Servers**: Support for remote MCP server connections
- **Advanced Context**: Dynamic schema updates and relationship discovery

## 📝 Database Context Configuration

The AI assistant uses a static database description file to understand your database structure without requiring direct database access for schema discovery.

### Configuring Database Description

1. **Edit the description file**: `docs/database-description.md`
2. **Describe your tables**: Include table names, key columns, and relationships
3. **Explain query patterns**: Help the AI understand how to join tables effectively
4. **Use clear language**: Write descriptions that help the AI generate accurate queries

### Example Description Format

```markdown
# Database Structure Overview

## Core Tables

### Athletes and Teams
- **master_athlete table**: Contains athlete information including names and identifiers
  - Use `master_athlete_id` to retrieve athlete names and details

### Team Management
- **roster_team table**: Contains event team information and roster data
  - Event teams are located in this table
  - Links athletes to specific teams and events

## Key Relationships
- Athletes are referenced throughout the system using `master_athlete_id`
- Use JOIN operations to connect athlete names with their team assignments
```

### Custom Description File

To use a different description file, set the environment variable:
```bash
CONTEXT_DESCRIPTION_FILE=path/to/your/custom-description.md
```

## 🔄 Recent Major Changes

### Proper MCP Implementation (v4.0)
- **Official MCP SDK**: Uses `@modelcontextprotocol/sdk` for both client and server
- **Streamable HTTP Transport**: Proper MCP protocol communication over HTTP
- **MCP Client Architecture**: NestJS app connects as proper MCP client
- **Zero Database Dependencies**: NestJS app completely isolated from database connectivity
- **Simplified Configuration**: Single `MCP_SERVERS` environment variable for multiple servers
- **Removed Custom Wrappers**: No more custom HTTP wrappers, uses official MCP protocol
- **Proper Tool Discovery**: Uses MCP `listTools()` instead of custom endpoints
- **Standard Tool Execution**: Uses MCP `callTool()` for proper protocol compliance

### Key Questions for Future Development

1. **Database Schema Evolution**: How should the system handle database schema changes during runtime? Should it auto-refresh or require manual triggers?

2. **Context Optimization**: What's the optimal balance between including comprehensive schema information vs. performance? Should we implement smart context filtering based on query patterns?

3. **Multi-Database Strategy**: When adding support for multiple databases, should each have its own MCP server instance, or should we implement a unified multi-database MCP server?

4. **Security Model**: What authentication and authorization model should be implemented? Role-based access to specific tables/schemas?

5. **Performance Scaling**: At what point should we implement query result caching, and what caching strategy would work best with the conversational AI pattern?

6. **Remote MCP Integration**: What protocols and security measures should be implemented for remote MCP server connections? How should we handle network failures and reconnection?

7. **Context Personalization**: Should the system learn from user query patterns to automatically adjust context inclusion for better AI responses?
