export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',

  openai: {
    apiKey: process.env.OPENAI_API_KEY || 'lm-studio',
    baseURL: process.env.OPENAI_BASE_URL || 'http://127.0.0.1:1234/v1',
    model: process.env.OPENAI_MODEL || 'devstral-small-2505-mlx',
    temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.1,
  },

  mcp: {
    // Parse comma-separated server URLs
    servers: (process.env.MCP_SERVERS || '')
      .split(',')
      .map((url, index) => ({
        name: `mcp-server-${index + 1}`,
        url: url.trim(),
      })),
  },

  context: {
    descriptionFile: process.env.CONTEXT_DESCRIPTION_FILE || 'docs/database-description.md',
    systemPromptFile: process.env.CONTEXT_SYSTEM_PROMPT_FILE || 'docs/system-prompt.txt',
  },
});
