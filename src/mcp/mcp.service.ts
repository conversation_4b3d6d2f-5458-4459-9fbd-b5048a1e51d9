import { Injectable, Logger, OnModule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import OpenAI from 'openai';

export interface McpServerConfig {
  name: string;
  url: string;
}

interface McpClientConnection {
  name: string;
  url: string;
  client: Client;
  transport: StreamableHTTPClientTransport;
  isConnected: boolean;
}

@Injectable()
export class McpService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(McpService.name);
  private mcpClients = new Map<string, McpClientConnection>();
  private allTools: OpenAI.Chat.Completions.ChatCompletionTool[] = [];

  constructor(private configService: ConfigService) {}

  public async onModuleInit() {
    const mcpConfig = this.configService.get('mcp');

    if (!mcpConfig?.servers?.length) {
      this.logger.warn('No MCP servers configured, skipping initialization');
      return;
    }

    await this.initializeMcpClients(mcpConfig.servers);
    this.logger.log('MCP Service initialized successfully');
  }

  async onModuleDestroy() {
    await this.disconnectAllClients();
    this.logger.log('MCP Service destroyed');
  }

  private async initializeMcpClients(servers: McpServerConfig[]) {
    this.logger.log(`Initializing ${servers.length} MCP clients`);

    for (const serverConfig of servers) {
      try {
        await this.initializeMcpClient(serverConfig);
      } catch (error) {
        this.logger.error(`Failed to initialize MCP client for ${serverConfig.name}: ${error.message}`, error.stack);
        // Continue with other servers even if one fails
      }
    }

    this.logger.log(`Successfully initialized ${this.mcpClients.size} MCP clients`);
  }

  private async initializeMcpClient(serverConfig: McpServerConfig) {
    this.logger.log(`Connecting to MCP server: ${serverConfig.name} at ${serverConfig.url}`);

    try {
      // Create MCP client
      const client = new Client({
        name: 'sw-search-agent-client',
        version: '1.0.0'
      });

      // Create HTTP transport
      const transport = new StreamableHTTPClientTransport(new URL(serverConfig.url + '/mcp'));

      // Create client connection
      const clientConnection: McpClientConnection = {
        name: serverConfig.name,
        url: serverConfig.url,
        client,
        transport,
        isConnected: false,
      };

      // Connect to the MCP server
      await client.connect(transport);
      clientConnection.isConnected = true;
      this.mcpClients.set(serverConfig.name, clientConnection);

      // Load tools from the MCP server
      await this.loadToolsFromMcpClient(clientConnection);

      this.logger.log(`Successfully connected to MCP server: ${serverConfig.name}`);
    } catch (error) {
      this.logger.error(`Failed to connect to MCP server ${serverConfig.name}: ${error.message}`);
      // Add fallback tools for development
      this.addFallbackTools();
    }
  }

  private async loadToolsFromMcpClient(client: McpClientConnection): Promise<void> {
    try {
      // List tools using the MCP client
      const toolsResponse = await client.client.listTools();
      const tools = toolsResponse.tools || [];

      // Convert MCP tools to OpenAI format
      const openaiTools: OpenAI.Chat.Completions.ChatCompletionTool[] = tools.map(tool => ({
        type: 'function' as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.inputSchema,
        },
      }));

      this.allTools.push(...openaiTools);
      this.logger.log(`Loaded ${openaiTools.length} tools from MCP server ${client.name}: ${openaiTools.map(t => t.function.name).join(', ')}`);
    } catch (error) {
      this.logger.error(`Failed to load tools from MCP server ${client.name}: ${error.message}`);
      throw error;
    }
  }

  private addFallbackTools(): void {
    // Add basic fallback tools for development when MCP server is not available
    const fallbackTools: OpenAI.Chat.Completions.ChatCompletionTool[] = [
      {
        type: 'function',
        function: {
          name: 'read_query',
          description: 'Execute a read-only SQL query (fallback mode)',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'The SQL query to execute'
              }
            },
            required: ['query']
          }
        }
      }
    ];

    this.allTools.push(...fallbackTools);
    this.logger.log(`Added ${fallbackTools.length} fallback tools: ${fallbackTools.map(t => t.function.name).join(', ')}`);
  }

  async getAvailableTools(): Promise<OpenAI.Chat.Completions.ChatCompletionTool[]> {
    this.logger.log(`Providing ${this.allTools.length} available MCP tools from ${this.mcpClients.size} servers`);
    this.logger.log(`Available tools: ${this.allTools.map(t => t.function.name).join(', ')}`);
    return this.allTools;
  }

  async executeTool(toolCall: OpenAI.Chat.Completions.ChatCompletionMessageToolCall): Promise<string> {
    this.logger.log(`Executing tool: ${toolCall.function.name} with arguments: ${toolCall.function.arguments}`);

    try {
      const startTime = Date.now();
      const args = JSON.parse(toolCall.function.arguments);

      // Find the appropriate MCP client for this tool
      const client = this.findClientForTool(toolCall.function.name);

      let result: string;
      if (client && client.isConnected) {
        result = await this.executeToolOnMcpClient(client, toolCall.function.name, args);
      } else {
        result = await this.executeFallbackTool(toolCall.function.name, args);
      }

      const duration = Date.now() - startTime;
      this.logger.log(`Tool ${toolCall.function.name} executed in ${duration}ms`);

      return result;
    } catch (error) {
      this.logger.error(`Tool execution failed for ${toolCall.function.name}: ${error.message}`, error.stack);
      return `Tool execution failed: ${error.message}`;
    }
  }

  private findClientForTool(toolName: string): McpClientConnection | null {
    // For now, return the first connected client
    // In a more complex setup, we'd maintain a mapping of tools to clients
    for (const client of this.mcpClients.values()) {
      if (client.isConnected) {
        return client;
      }
    }
    return null;
  }

  private async executeToolOnMcpClient(client: McpClientConnection, toolName: string, args: any): Promise<string> {
    this.logger.log(`Executing ${toolName} on MCP server ${client.name} at ${client.url}`);

    try {
      // Call tool using the MCP client
      const result = await client.client.callTool({
        name: toolName,
        arguments: args,
      });

      // Extract text content from the result
      const textContent = Array.isArray(result.content)
        ? result.content
            .filter((item: any) => item.type === 'text')
            .map((item: any) => item.text)
            .join('\n')
        : String(result.content);

      return textContent || 'No result returned';
    } catch (error) {
      this.logger.error(`Failed to execute tool ${toolName} on MCP server: ${error.message}`);
      throw error;
    }
  }

  private async executeFallbackTool(toolName: string, args: any): Promise<string> {
    this.logger.warn(`Executing fallback for tool: ${toolName}`);

    switch (toolName) {
      case 'read_query':
        return `[Fallback Mode] Mock result for query: ${args.query}\n\nMCP server not available - this is a fallback response.`;
      case 'list_tables':
        return '[Fallback Mode] Mock table list: master_athlete, roster_team, results';
      case 'describe_table':
        return `[Fallback Mode] Mock schema for table: ${args.table_name}\n\nMCP server not available - this is a fallback response.`;
      default:
        return `[Fallback Mode] Unknown tool: ${toolName}`;
    }
  }



  private async disconnectAllClients() {
    this.logger.log('Disconnecting all MCP clients');

    for (const [serverName, client] of this.mcpClients) {
      try {
        if (client.client) {
          await client.client.close();
        }
        client.isConnected = false;
        this.logger.log(`Disconnected from MCP server: ${serverName}`);
      } catch (error) {
        this.logger.warn(`Failed to disconnect from MCP server ${serverName}: ${error.message}`);
      }
    }

    this.mcpClients.clear();
  }




}
