import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SearchController } from './search.controller';
import { SearchService } from './search.service';
import { OpenaiModule } from '../openai/openai.module';
import { McpModule } from '../mcp/mcp.module';
import { ContextModule } from '../context/context.module';

@Module({
  imports: [OpenaiModule, McpModule, ContextModule],
  controllers: [SearchController],
  providers: [SearchService],
})
export class SearchModule {}
