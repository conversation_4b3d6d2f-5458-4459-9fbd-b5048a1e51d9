<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SW Search Agent</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>SW Search Agent</h1>
            <p>Search and analyze your PostgreSQL database using AI</p>
        </header>

        <main>
            <div class="search-section">
                <form id="searchForm">
                    <div class="input-group">
                        <textarea 
                            id="queryInput" 
                            placeholder="Ask me anything about your database... (e.g., 'Show me all tables', 'Find users created last month', 'What's the schema of the products table?')"
                            rows="3"
                            required
                        ></textarea>
                        <button type="submit" id="searchButton">
                            <span id="buttonText">Search</span>
                            <span id="loadingSpinner" class="spinner hidden">⟳</span>
                        </button>
                    </div>
                </form>
            </div>

            <div class="results-section">
                <div id="conversationContainer" class="conversation-container">
                    <!-- Conversation messages will be added here -->
                </div>
            </div>

            <div class="conversation-controls">
                <button id="newConversationBtn" class="secondary-btn">New Conversation</button>
                <button id="clearConversationBtn" class="secondary-btn">Clear Current</button>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
