FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy server implementation
COPY mcp-postgres-http.js ./

EXPOSE 3001

ENTRYPOINT ["dumb-init", "--"]

CMD ["node", "/app/mcp-postgres-http.js"]
