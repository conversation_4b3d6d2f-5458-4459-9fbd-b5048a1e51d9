# Dockerfile for MCP PostgreSQL server with HTTP transport
FROM node:18-alpine

# Install required packages
RUN npm install -g @modelcontextprotocol/sdk express pg zod

# Create app directory
WORKDIR /app

# Copy MCP server implementation
COPY mcp-postgres-http.js /app/
COPY start-mcp-server.sh /usr/local/bin/start-mcp-server.sh
RUN chmod +x /usr/local/bin/start-mcp-server.sh

# Expose port for HTTP API
EXPOSE 3001

# Start the MCP server
CMD ["node", "/app/mcp-postgres-http.js"]
