#!/bin/sh

# Modern MCP PostgreSQL Server Startup Script
# This script starts the enhanced MCP server with proper configuration

set -e

# Configuration validation
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is required"
    exit 1
fi

# Set defaults for optional environment variables
export HTTP_PORT=${HTTP_PORT:-3001}
export MAX_CONNECTIONS=${MAX_CONNECTIONS:-10}
export SESSION_TIMEOUT=${SESSION_TIMEOUT:-300000}
export ENABLE_STATELESS=${ENABLE_STATELESS:-false}
export LOG_LEVEL=${LOG_LEVEL:-info}

# Log startup configuration
echo "🚀 Starting Modern MCP PostgreSQL Server v2.0.0"
echo "📡 Port: $HTTP_PORT"
echo "🗄️  Database: $(echo $DATABASE_URL | sed 's/\/\/.*@/\/\/***:***@/')"
echo "⚙️  Mode: $([ "$ENABLE_STATELESS" = "true" ] && echo "Stateless" || echo "Stateful")"
echo "🔧 Max Connections: $MAX_CONNECTIONS"
echo "⏱️  Session Timeout: ${SESSION_TIMEOUT}ms"
echo "📝 Log Level: $LOG_LEVEL"
echo ""

# Test database connectivity before starting
echo "🔍 Testing database connectivity..."
node -e "
const { Client } = require('pg');
const client = new Client('$DATABASE_URL');
client.connect()
  .then(() => client.query('SELECT 1'))
  .then(() => {
    console.log('✅ Database connection successful');
    return client.end();
  })
  .catch((err) => {
    console.error('❌ Database connection failed:', err.message);
    process.exit(1);
  });
"

echo "🎯 Starting MCP server..."
echo ""

# Start the enhanced MCP server
exec node /app/mcp-postgres-http.js
