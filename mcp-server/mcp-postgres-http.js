#!/usr/bin/env node

/**
 * HTTP-based MCP PostgreSQL server using the official MCP SDK
 * This creates a proper MCP server with HTTP transport
 */

const express = require('express');
const { McpServer } = require('@modelcontextprotocol/sdk/server/mcp.js');
const { StreamableHTTPServerTransport } = require('@modelcontextprotocol/sdk/server/streamableHttp.js');
const { z } = require('zod');
const pg = require('pg');

const app = express();
const port = process.env.HTTP_PORT || 3001;
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('DATABASE_URL environment variable is required');
  process.exit(1);
}

// Create PostgreSQL client
const pgClient = new pg.Client(databaseUrl);

// Connect to database
async function initializeDatabase() {
  try {
    await pgClient.connect();
    console.log('Connected to PostgreSQL database');
  } catch (error) {
    console.error('Failed to connect to database:', error);
    process.exit(1);
  }
}

// Create MCP server
const server = new McpServer({
  name: 'postgres-mcp-server',
  version: '1.0.0'
});

// Add query tool
server.tool(
  'read_query',
  {
    query: z.string().describe('The SQL query to execute (SELECT statements only)')
  },
  async ({ query }) => {
    try {
      // Ensure it's a read-only query
      const trimmedQuery = query.trim().toLowerCase();
      if (!trimmedQuery.startsWith('select') && !trimmedQuery.startsWith('with')) {
        throw new Error('Only SELECT and WITH queries are allowed');
      }

      // Execute query in a read-only transaction
      await pgClient.query('BEGIN READ ONLY');
      const result = await pgClient.query(query);
      await pgClient.query('COMMIT');

      // Format result
      const rows = result.rows;
      const formattedResult = rows.length > 0 
        ? JSON.stringify(rows, null, 2)
        : 'No rows returned';

      return {
        content: [{
          type: 'text',
          text: `Query executed successfully. Returned ${rows.length} rows:\n\n${formattedResult}`
        }]
      };
    } catch (error) {
      // Rollback transaction on error
      try {
        await pgClient.query('ROLLBACK');
      } catch (rollbackError) {
        console.error('Rollback error:', rollbackError);
      }

      return {
        content: [{
          type: 'text',
          text: `Query execution failed: ${error.message}`
        }],
        isError: true
      };
    }
  }
);

// Add list tables tool
server.tool(
  'list_tables',
  {},
  async () => {
    try {
      const result = await pgClient.query(`
        SELECT table_name, table_schema 
        FROM information_schema.tables 
        WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
        ORDER BY table_schema, table_name
      `);

      const tables = result.rows.map(row => `${row.table_schema}.${row.table_name}`);
      
      return {
        content: [{
          type: 'text',
          text: `Available tables:\n${tables.join('\n')}`
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Failed to list tables: ${error.message}`
        }],
        isError: true
      };
    }
  }
);

// Add describe table tool
server.tool(
  'describe_table',
  {
    table_name: z.string().describe('Name of the table to describe')
  },
  async ({ table_name }) => {
    try {
      const result = await pgClient.query(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_name = $1
        ORDER BY ordinal_position
      `, [table_name]);

      if (result.rows.length === 0) {
        return {
          content: [{
            type: 'text',
            text: `Table '${table_name}' not found`
          }],
          isError: true
        };
      }

      const schema = result.rows.map(row => ({
        column: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable === 'YES',
        default: row.column_default,
        max_length: row.character_maximum_length
      }));

      return {
        content: [{
          type: 'text',
          text: `Schema for table '${table_name}':\n${JSON.stringify(schema, null, 2)}`
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Failed to describe table: ${error.message}`
        }],
        isError: true
      };
    }
  }
);

// Middleware
app.use(express.json());

// Map to store transports by session ID
const transports = {};

// Handle POST requests for client-to-server communication
app.post('/mcp', async (req, res) => {
  try {
    // Create a new transport for each request (stateless mode)
    const transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: undefined, // Stateless mode
    });

    // Clean up transport when response closes
    res.on('close', () => {
      transport.close();
    });

    // Connect server to transport
    await server.connect(transport);
    
    // Handle the request
    await transport.handleRequest(req, res, req.body);
  } catch (error) {
    console.error('Error handling MCP request:', error);
    if (!res.headersSent) {
      res.status(500).json({
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: 'Internal server error',
        },
        id: null,
      });
    }
  }
});

// Handle GET requests (not supported in stateless mode)
app.get('/mcp', async (req, res) => {
  res.status(405).json({
    jsonrpc: "2.0",
    error: {
      code: -32000,
      message: "Method not allowed in stateless mode."
    },
    id: null
  });
});

// Handle DELETE requests (not supported in stateless mode)
app.delete('/mcp', async (req, res) => {
  res.status(405).json({
    jsonrpc: "2.0",
    error: {
      code: -32000,
      message: "Method not allowed in stateless mode."
    },
    id: null
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'mcp-postgres-server',
    database: 'connected'
  });
});

// Start the server
async function startServer() {
  await initializeDatabase();

  app.listen(port, '0.0.0.0', () => {
    console.log(`MCP PostgreSQL server listening on port ${port}`);
    console.log(`Database URL: ${databaseUrl}`);
    console.log('Available endpoints:');
    console.log('  GET  /health - Health check');
    console.log('  POST /mcp - MCP protocol endpoint');
  });
}

startServer().catch(console.error);

// Graceful shutdown
const shutdown = async () => {
  console.log('Shutting down gracefully...');
  try {
    await pgClient.end();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
  process.exit(0);
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);
