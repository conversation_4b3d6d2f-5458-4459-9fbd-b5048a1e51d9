#!/usr/bin/env node

/**
 * MCP PostgreSQL Server with Streamable HTTP Transport
 * Based on official @modelcontextprotocol/server-postgres with HTTP transport
 */

const express = require('express');
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StreamableHTTPServerTransport } = require('@modelcontextprotocol/sdk/server/streamableHttp.js');
const {
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  ReadResourceRequestSchema,
} = require('@modelcontextprotocol/sdk/types.js');
const pg = require('pg');

const args = process.argv.slice(2);
if (args.length === 0 && !process.env.DATABASE_URL) {
  console.error('Please provide a database URL as a command-line argument or DATABASE_URL environment variable');
  process.exit(1);
}

const databaseUrl = args[0] || process.env.DATABASE_URL;
const resourceBaseUrl = new URL(databaseUrl);
resourceBaseUrl.protocol = 'postgres:';
resourceBaseUrl.password = '';

const pool = new pg.Pool({
  connectionString: databaseUrl,
});

const SCHEMA_PATH = 'schema';

const server = new Server({
  name: 'example-servers/postgres',
  version: '0.1.0',
}, {
  capabilities: {
    resources: {},
    tools: {},
  },
});

server.setRequestHandler(ListResourcesRequestSchema, async () => {
  const client = await pool.connect();
  try {
    const result = await client.query("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");
    return {
      resources: result.rows.map((row) => ({
        uri: new URL(`${row.table_name}/${SCHEMA_PATH}`, resourceBaseUrl).href,
        mimeType: 'application/json',
        name: `"${row.table_name}" database schema`,
      })),
    };
  } finally {
    client.release();
  }
});

server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
  const resourceUrl = new URL(request.params.uri);
  const pathComponents = resourceUrl.pathname.split('/');
  const schema = pathComponents.pop();
  const tableName = pathComponents.pop();

  if (schema !== SCHEMA_PATH) {
    throw new Error('Invalid resource URI');
  }

  const client = await pool.connect();
  try {
    const result = await client.query('SELECT column_name, data_type FROM information_schema.columns WHERE table_name = $1', [tableName]);
    return {
      contents: [
        {
          uri: request.params.uri,
          mimeType: 'application/json',
          text: JSON.stringify(result.rows, null, 2),
        },
      ],
    };
  } finally {
    client.release();
  }
});

server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'query',
        description: 'Run a read-only SQL query',
        inputSchema: {
          type: 'object',
          properties: {
            sql: { type: 'string' },
          },
        },
      },
    ],
  };
});

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  if (request.params.name === 'query') {
    const sql = request.params.arguments?.sql;
    const client = await pool.connect();
    try {
      await client.query('BEGIN TRANSACTION READ ONLY');
      const result = await client.query(sql);
      return {
        content: [{ type: 'text', text: JSON.stringify(result.rows, null, 2) }],
        isError: false,
      };
    } catch (error) {
      throw error;
    } finally {
      client
        .query('ROLLBACK')
        .catch((error) => console.warn('Could not roll back transaction:', error));
      client.release();
    }
  }
  throw new Error(`Unknown tool: ${request.params.name}`);
});

// Express app setup
const app = express();
app.use(express.json());

app.all('/mcp', async (req, res) => {
  try {
    const transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: undefined, // Stateless mode
    });

    // Clean up transport when response closes
    res.on('close', () => {
      transport.close();
    });

    // Connect server to transport
    await server.connect(transport);

    // Handle the request
    await transport.handleRequest(req, res, req.body);
  } catch (error) {
    console.error('Error handling MCP request:', error);
    if (!res.headersSent) {
      res.status(500).json({
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: 'Internal server error',
        },
        id: null,
      });
    }
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'mcp-postgres-server',
    database: 'connected'
  });
});

// Start the server
const port = process.env.HTTP_PORT || 3001;

app.listen(port, '0.0.0.0', () => {
  console.log(`MCP PostgreSQL server listening on port ${port}`);
  console.log(`Database URL: ${databaseUrl.replace(/\/\/.*@/, '//***:***@')}`);
  console.log('Available endpoints:');
  console.log('  GET  /health - Health check');
  console.log('  ALL  /mcp - MCP protocol endpoint');
});

// Graceful shutdown
const shutdown = async () => {
  console.log('Shutting down gracefully...');
  try {
    await pool.end();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
  process.exit(0);
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);


