{"name": "mcp-postgres-server", "version": "2.0.0", "description": "Modern PostgreSQL MCP Server with Streamable HTTP Transport", "main": "mcp-postgres-http.js", "scripts": {"start": "node mcp-postgres-http.js", "dev": "node --inspect=0.0.0.0:9229 mcp-postgres-http.js", "test": "node -e \"console.log('Health check test'); require('http').get('http://localhost:3001/health', (res) => { console.log('Status:', res.statusCode); process.exit(res.statusCode === 200 ? 0 : 1); }).on('error', () => process.exit(1));\""}, "keywords": ["mcp", "model-context-protocol", "postgresql", "database", "ai", "llm", "streamable-http"], "author": "SW Search Agent Team", "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "express": "^4.18.2", "pg": "^8.11.3", "zod": "^3.23.8"}, "optionalDependencies": {"dumb-init": "^1.2.5"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/sw-search-agent.git", "directory": "mcp-server"}, "bugs": {"url": "https://github.com/your-org/sw-search-agent/issues"}, "homepage": "https://github.com/your-org/sw-search-agent#readme"}