{"name": "mcp-postgres-server", "version": "2.0.0", "description": "Modern PostgreSQL MCP Server with Streamable HTTP Transport", "main": "mcp-postgres-http.js", "scripts": {"start": "node mcp-postgres-http.js"}, "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "express": "^4.18.2", "pg": "^8.11.3"}, "optionalDependencies": {"dumb-init": "^1.2.5"}}