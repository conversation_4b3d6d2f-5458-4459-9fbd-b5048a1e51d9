# Modern PostgreSQL MCP Server

A comprehensive Model Context Protocol (MCP) server implementation for PostgreSQL database access, built with the official MCP SDK and Streamable HTTP transport.

## Features

### 🚀 Modern Architecture
- **Official MCP SDK**: Built with `@modelcontextprotocol/sdk` v1.12.1+
- **Streamable HTTP Transport**: Proper MCP protocol implementation over HTTP
- **Dual Mode Support**: Both stateless and stateful operation modes
- **Session Management**: Automatic session cleanup and timeout handling
- **Connection Pooling**: Efficient PostgreSQL connection management

### 🛠️ Comprehensive Tools
- **`read_query`**: Execute read-only SQL queries with safety validation
- **`list_tables`**: List database tables and views with filtering options
- **`describe_table`**: Get detailed table schema, constraints, and indexes
- **`list_schemas`**: List available database schemas
- **`get_table_stats`**: Retrieve table statistics and column information

### 🔒 Security Features
- **Read-Only Access**: Only SELECT, WITH, SHOW, EXPLAIN, DESCRIBE queries allowed
- **Query Validation**: Multiple layers of SQL injection protection
- **Connection Isolation**: Proper connection pooling and cleanup
- **Non-Root Container**: Runs as unprivileged user in Docker

### 📊 Monitoring & Observability
- **Health Checks**: Comprehensive health endpoint with database status
- **Structured Logging**: Configurable log levels with contextual information
- **Performance Metrics**: Query timing and connection pool statistics
- **Session Tracking**: Active session monitoring in stateful mode

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | *required* | PostgreSQL connection string |
| `HTTP_PORT` | `3001` | HTTP server port |
| `MAX_CONNECTIONS` | `10` | Maximum database connections |
| `SESSION_TIMEOUT` | `300000` | Session timeout in milliseconds (5 min) |
| `ENABLE_STATELESS` | `false` | Enable stateless mode |
| `LOG_LEVEL` | `info` | Logging level (debug, info, warn, error) |

### Example Configuration

```bash
# Required
DATABASE_URL=************************************/database

# Optional
HTTP_PORT=3001
MAX_CONNECTIONS=20
SESSION_TIMEOUT=600000
ENABLE_STATELESS=false
LOG_LEVEL=info
```

## API Endpoints

### MCP Protocol
- **`POST /mcp`**: Main MCP protocol endpoint
- **`GET /mcp`**: Server-to-client notifications (stateful mode only)
- **`DELETE /mcp`**: Session termination (stateful mode only)

### Monitoring
- **`GET /health`**: Health check with database status
- **`GET /info`**: Server information and capabilities

## Operation Modes

### Stateful Mode (Default)
- Maintains persistent sessions between requests
- Supports server-to-client notifications via SSE
- Better performance for multiple requests
- Automatic session cleanup

### Stateless Mode
- Creates new server instance for each request
- No session management overhead
- Better for horizontally scaled deployments
- Simpler architecture

## Docker Usage

### Build and Run
```bash
# Build the container
docker build -t mcp-postgres-server .

# Run with environment variables
docker run -d \
  --name mcp-postgres \
  -p 3001:3001 \
  -e DATABASE_URL=********************************/db \
  -e LOG_LEVEL=info \
  mcp-postgres-server
```

### Health Check
```bash
# Check server health
curl http://localhost:3001/health

# Get server information
curl http://localhost:3001/info
```

## Development

### Local Development
```bash
# Install dependencies
npm install

# Start in development mode
npm run dev

# Run health check test
npm test
```

### Debugging
```bash
# Enable debug logging
LOG_LEVEL=debug npm start

# Run with Node.js inspector
npm run dev
```

## Tool Examples

### Execute Query
```json
{
  "name": "read_query",
  "arguments": {
    "query": "SELECT * FROM users WHERE active = true",
    "limit": 100
  }
}
```

### List Tables
```json
{
  "name": "list_tables",
  "arguments": {
    "schema": "public",
    "include_system": false
  }
}
```

### Describe Table
```json
{
  "name": "describe_table",
  "arguments": {
    "table_name": "public.users",
    "include_indexes": true,
    "include_constraints": true
  }
}
```

## Migration from v1.x

The new v2.0 implementation includes breaking changes:

### What's New
- ✅ Official MCP SDK integration
- ✅ Proper Streamable HTTP transport
- ✅ Enhanced tool capabilities
- ✅ Better error handling and logging
- ✅ Connection pooling
- ✅ Session management

### Migration Steps
1. Update environment variables (see configuration above)
2. Test with new health check endpoints
3. Update client code to use new tool parameters
4. Monitor logs for any compatibility issues

## Troubleshooting

### Common Issues

**Connection Refused**
- Check `DATABASE_URL` format
- Verify database server is accessible
- Check firewall settings

**Permission Denied**
- Ensure database user has SELECT permissions
- Check schema access rights

**Session Timeout**
- Increase `SESSION_TIMEOUT` value
- Consider using stateless mode for long-running operations

### Logs
```bash
# View container logs
docker logs mcp-postgres

# Follow logs in real-time
docker logs -f mcp-postgres
```

## License

MIT License - see LICENSE file for details.
